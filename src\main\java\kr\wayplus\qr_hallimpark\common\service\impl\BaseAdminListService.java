package kr.wayplus.qr_hallimpark.common.service.impl;

import kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper;
import kr.wayplus.qr_hallimpark.common.service.AdminListService;
import kr.wayplus.qr_hallimpark.model.AdminListResponse;
import kr.wayplus.qr_hallimpark.model.AdminListSearch;
import lombok.extern.slf4j.Slf4j;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;

/**
 * 관리자 리스트 페이지 공통 서비스 기본 구현체
 * - 모든 관리자 리스트 서비스에서 상속하여 사용할 수 있는 기본 구현
 * - 표준적인 검색/필터/정렬/페이징 로직 제공
 */
@Slf4j
@Transactional(readOnly = true)
public abstract class BaseAdminListService<T> implements AdminListService<T> {
    
    /**
     * 각 서비스에서 구현해야 할 매퍼 반환 메서드
     * @return 해당 도메인의 매퍼
     */
    protected abstract AdminListMapper<T> getMapper();
    
    /**
     * 각 서비스에서 구현해야 할 테이블명 반환 메서드
     * @return 해당 도메인의 테이블명
     */
    protected abstract String getTableName();
    
    /**
     * 검색 조건에 따른 리스트 조회
     * @param searchCondition 검색 조건
     * @return 페이징된 리스트 응답
     */
    @Override
    public AdminListResponse<T> findListWithConditions(AdminListSearch searchCondition) {
        try {
            // 검색 조건 유효성 검사
            validateSearchCondition(searchCondition);
            
            // 테이블명 설정
            searchCondition.setTableName(getTableName());
            
            log.debug("Searching list with conditions: {}", searchCondition);
            
            // 전체 개수 조회
            Long totalCount = countWithConditions(searchCondition);
            
            if (totalCount == 0) {
                log.debug("No data found for conditions: {}", searchCondition);
                return AdminListResponse.empty(searchCondition);
            }
            
            // 리스트 조회
            List<T> items = getMapper().selectListWithConditions(searchCondition);
            
            log.debug("Found {} items out of {} total", items.size(), totalCount);
            
            // 응답 생성
            return AdminListResponse.success(items, searchCondition, totalCount);
            
        } catch (Exception e) {
            log.error("Error occurred while searching list", e);
            return AdminListResponse.error("데이터 조회 중 오류가 발생했습니다: " + e.getMessage(), "SEARCH_ERROR");
        }
    }
    
    /**
     * 전체 데이터 개수 조회 (검색 조건 적용)
     * @param searchCondition 검색 조건
     * @return 전체 데이터 개수
     */
    @Override
    public Long countWithConditions(AdminListSearch searchCondition) {
        try {
            // 검색 조건 유효성 검사
            validateSearchCondition(searchCondition);
            
            // 테이블명 설정
            searchCondition.setTableName(getTableName());
            
            Long count = getMapper().countWithConditions(searchCondition);
            log.debug("Total count for conditions: {}", count);
            
            return count != null ? count : 0L;
            
        } catch (Exception e) {
            log.error("Error occurred while counting data", e);
            return 0L;
        }
    }
    
    /**
     * 기본 검색 조건 생성 (각 서비스에서 오버라이드 가능)
     * @return 기본 검색 조건
     */
    @Override
    public AdminListSearch createDefaultSearchCondition() {
        return AdminListSearch.builder()
                .page(1)
                .size(20)
                .sortField(getDefaultSortField())
                .sortDirection("DESC")
                .baseCondition(getDefaultBaseCondition())
                .tableName(getTableName())
                .build();
    }
    
    /**
     * 기본 정렬 필드 반환 (각 서비스에서 오버라이드 가능)
     * @return 기본 정렬 필드
     */
    protected String getDefaultSortField() {
        return "create_date";
    }
    
    /**
     * 기본 WHERE 조건 반환 (각 서비스에서 오버라이드 필수)
     * @return 기본 WHERE 조건
     */
    protected String getDefaultBaseCondition() {
        // 각 서비스에서 적절한 테이블 별칭과 함께 오버라이드해야 함
        return null;
    }
    
    /**
     * 검색 조건 전처리 (각 서비스에서 오버라이드 가능)
     * - 특정 도메인에서 추가적인 검색 조건 처리가 필요한 경우 사용
     * @param searchCondition 검색 조건
     */
    protected void preprocessSearchCondition(AdminListSearch searchCondition) {
        // 기본 구현은 아무것도 하지 않음
        // 각 서비스에서 필요에 따라 오버라이드
    }
    
    /**
     * 검색 결과 후처리 (각 서비스에서 오버라이드 가능)
     * - 조회된 데이터에 추가적인 처리가 필요한 경우 사용
     * @param items 조회된 데이터 목록
     * @param searchCondition 검색 조건
     */
    protected void postprocessSearchResult(List<T> items, AdminListSearch searchCondition) {
        // 기본 구현은 아무것도 하지 않음
        // 각 서비스에서 필요에 따라 오버라이드
    }
    
    /**
     * 검색 조건 유효성 검사 (확장된 버전)
     * @param searchCondition 검색 조건
     */
    @Override
    public void validateSearchCondition(AdminListSearch searchCondition) {
        // 기본 유효성 검사 수행
        AdminListService.super.validateSearchCondition(searchCondition);
        
        // 전처리 수행
        preprocessSearchCondition(searchCondition);
        
        // 추가 유효성 검사
        validateDomainSpecificConditions(searchCondition);
    }
    
    /**
     * 도메인별 특수 조건 유효성 검사 (각 서비스에서 오버라이드 가능)
     * @param searchCondition 검색 조건
     */
    protected void validateDomainSpecificConditions(AdminListSearch searchCondition) {
        // 기본 구현은 아무것도 하지 않음
        // 각 서비스에서 필요에 따라 오버라이드
    }
    
    /**
     * 페이징 정보 유효성 검사
     * @param searchCondition 검색 조건
     * @param totalCount 전체 데이터 개수
     */
    protected void validatePagingInfo(AdminListSearch searchCondition, Long totalCount) {
        if (totalCount > 0) {
            int maxPage = (int) Math.ceil((double) totalCount / searchCondition.getSize());
            if (searchCondition.getPage() > maxPage) {
                log.warn("Requested page {} exceeds max page {}. Adjusting to max page.", 
                        searchCondition.getPage(), maxPage);
                searchCondition.setPage(maxPage);
            }
        }
    }
    
    /**
     * 검색 필드 유효성 검사
     * @param searchCondition 검색 조건
     * @param allowedFields 허용된 검색 필드 목록
     */
    protected void validateSearchFields(AdminListSearch searchCondition, List<String> allowedFields) {
        if (searchCondition.getSearchFields() != null && !searchCondition.getSearchFields().isEmpty()) {
            List<String> validFields = searchCondition.getSearchFields().stream()
                    .filter(allowedFields::contains)
                    .toList();
            
            if (validFields.isEmpty()) {
                log.warn("No valid search fields found. Using default fields.");
                searchCondition.setSearchFields(null);
            } else {
                searchCondition.setSearchFields(validFields);
            }
        }
    }
    
    /**
     * 정렬 필드 유효성 검사
     * @param searchCondition 검색 조건
     * @param allowedSortFields 허용된 정렬 필드 목록
     */
    protected void validateSortField(AdminListSearch searchCondition, List<String> allowedSortFields) {
        if (searchCondition.getSortField() != null && 
            !allowedSortFields.contains(searchCondition.getSortField())) {
            log.warn("Invalid sort field: {}. Using default sort field.", searchCondition.getSortField());
            searchCondition.setSortField(getDefaultSortField());
        }
    }

    /**
     * 검색 조건을 Map으로 변환 (MyBatis 파라미터용)
     * @param searchCondition 검색 조건
     * @return Map 형태의 파라미터
     */
    protected java.util.Map<String, Object> convertToParameterMap(AdminListSearch searchCondition) {
        java.util.Map<String, Object> paramMap = new java.util.HashMap<>();
        paramMap.put("searchCondition", searchCondition);
        paramMap.put("tableName", getTableName());
        return paramMap;
    }
}
